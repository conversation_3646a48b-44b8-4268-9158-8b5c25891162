Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    main.o(i.DMA1_Channel1_IRQHandler) refers to stm32f10x_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    main.o(i.DMA1_Channel1_IRQHandler) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    main.o(i.DMA1_Channel1_IRQHandler) refers to stm32f10x_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    main.o(i.DMA1_Channel1_IRQHandler) refers to main.o(.data) for currentadc
    main.o(i.DMA1_Channel1_IRQHandler) refers to main.o(.bss) for adcx
    main.o(i.EXTI0_IRQHandler) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.EXTI0_IRQHandler) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.EXTI0_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    main.o(i.EXTI0_IRQHandler) refers to main.o(.data) for show_flag
    main.o(i.EXTI0_IRQHandler) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.EXTI3_IRQHandler) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.EXTI3_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.EXTI3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_PrescalerConfig) for TIM_PrescalerConfig
    main.o(i.EXTI3_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    main.o(i.EXTI3_IRQHandler) refers to main.o(.data) for pre
    main.o(i.EXTI4_IRQHandler) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.EXTI4_IRQHandler) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    main.o(i.EXTI4_IRQHandler) refers to stm32f10x_tim.o(i.TIM_PrescalerConfig) for TIM_PrescalerConfig
    main.o(i.EXTI4_IRQHandler) refers to stm32f10x_exti.o(i.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    main.o(i.EXTI4_IRQHandler) refers to main.o(.data) for pre
    main.o(i.GetPowerMag) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(i.GetPowerMag) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(i.GetPowerMag) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(i.GetPowerMag) refers to cr4_fft_1024_stm32.o(.text) for cr4_fft_1024_stm32
    main.o(i.GetPowerMag) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(i.GetPowerMag) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.GetPowerMag) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    main.o(i.GetPowerMag) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.GetPowerMag) refers to sqrt.o(i.sqrt) for sqrt
    main.o(i.GetPowerMag) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.GetPowerMag) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    main.o(i.GetPowerMag) refers to frleqf.o(x$fpl$frleqf) for __aeabi_cfrcmple
    main.o(i.GetPowerMag) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.GetPowerMag) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.GetPowerMag) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.GetPowerMag) refers to noretval__2sprintf.o(.text) for __2sprintf
    main.o(i.GetPowerMag) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.GetPowerMag) refers to main.o(.bss) for fftin
    main.o(i.GetPowerMag) refers to main.o(.data) for temp
    main.o(i.InitBufInArray) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.InitBufInArray) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.InitBufInArray) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.InitBufInArray) refers to sin.o(i.sin) for sin
    main.o(i.InitBufInArray) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    main.o(i.InitBufInArray) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    main.o(i.InitBufInArray) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    main.o(i.InitBufInArray) refers to ffixu.o(x$fpl$ffixu) for __aeabi_f2uiz
    main.o(i.InitBufInArray) refers to main.o(.bss) for magout
    main.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_GetITStatus) for TIM_GetITStatus
    main.o(i.TIM3_IRQHandler) refers to main.o(i.sinout) for sinout
    main.o(i.TIM3_IRQHandler) refers to stm32f10x_tim.o(i.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    main.o(i.clear_point) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    main.o(i.clear_point) refers to main.o(i.lcd_huaxian) for lcd_huaxian
    main.o(i.clear_point) refers to main.o(i.lcd_huadian) for lcd_huadian
    main.o(i.clear_point) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.clear_point) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    main.o(i.clear_point) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.clear_point) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    main.o(i.clear_point) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    main.o(i.clear_point) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.clear_point) refers to main.o(.data) for T
    main.o(i.clear_point) refers to main.o(.bss) for adcx
    main.o(i.lcd_huadian) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    main.o(i.lcd_huaxian) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to dma.o(i.MYDMA1_Config) for MYDMA1_Config
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.main) refers to exti.o(i.EXTIX_Init) for EXTIX_Init
    main.o(i.main) refers to lcd.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to adc.o(i.Adc_Init) for Adc_Init
    main.o(i.main) refers to beep.o(i.BEEP_Init) for BEEP_Init
    main.o(i.main) refers to main.o(i.InitBufInArray) for InitBufInArray
    main.o(i.main) refers to timer.o(i.TIM4_Int_Init) for TIM4_Int_Init
    main.o(i.main) refers to timer.o(i.TIM3_Int_Init) for TIM3_Int_Init
    main.o(i.main) refers to timer.o(i.TIM2_PWM_Init) for TIM2_PWM_Init
    main.o(i.main) refers to dac.o(i.Dac1_Init) for Dac1_Init
    main.o(i.main) refers to dac.o(i.Dac2_Init) for Dac2_Init
    main.o(i.main) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    main.o(i.main) refers to main.o(i.window) for window
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to main.o(i.GetPowerMag) for GetPowerMag
    main.o(i.main) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    main.o(i.main) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    main.o(i.main) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    main.o(i.main) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    main.o(i.main) refers to main.o(i.clear_point) for clear_point
    main.o(i.main) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    main.o(i.main) refers to main.o(.data) for currentadc
    main.o(i.main) refers to main.o(.bss) for adcx
    main.o(i.main) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.sinout) refers to stm32f10x_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    main.o(i.sinout) refers to main.o(.bss) for magout
    main.o(i.sinout) refers to main.o(.data) for i
    main.o(i.window) refers to lcd.o(i.LCD_ShowString) for LCD_ShowString
    main.o(i.window) refers to lcd.o(i.LCD_ShowNum) for LCD_ShowNum
    main.o(i.window) refers to main.o(i.lcd_huaxian) for lcd_huaxian
    main.o(i.window) refers to main.o(i.lcd_huadian) for lcd_huadian
    main.o(i.window) refers to lcd.o(.data) for POINT_COLOR
    main.o(i.window) refers to main.o(.data) for V
    system_stm32f10x.o(i.SetSysClock) refers to system_stm32f10x.o(i.SetSysClockTo72) for SetSysClockTo72
    system_stm32f10x.o(i.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data) for SystemCoreClock
    system_stm32f10x.o(i.SystemInit) refers to system_stm32f10x.o(i.SetSysClock) for SetSysClock
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f10x_rcc.o(i.RCC_ADCCLKConfig) for RCC_ADCCLKConfig
    adc.o(i.Adc_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_DeInit) for ADC_DeInit
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_ResetCalibration) for ADC_ResetCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus) for ADC_GetResetCalibrationStatus
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_StartCalibration) for ADC_StartCalibration
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_GetCalibrationStatus) for ADC_GetCalibrationStatus
    adc.o(i.Adc_Init) refers to stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd) for ADC_SoftwareStartConvCmd
    beep.o(i.BEEP_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    beep.o(i.BEEP_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    beep.o(i.BEEP_Init) refers to stm32f10x_gpio.o(i.GPIO_ResetBits) for GPIO_ResetBits
    dac.o(i.Dac1_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    dac.o(i.Dac1_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    dac.o(i.Dac1_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    dac.o(i.Dac1_Init) refers to stm32f10x_dac.o(i.DAC_Init) for DAC_Init
    dac.o(i.Dac1_Init) refers to stm32f10x_dac.o(i.DAC_Cmd) for DAC_Cmd
    dac.o(i.Dac1_Init) refers to stm32f10x_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    dac.o(i.Dac2_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    dac.o(i.Dac2_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    dac.o(i.Dac2_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    dac.o(i.Dac2_Init) refers to stm32f10x_dac.o(i.DAC_Init) for DAC_Init
    dac.o(i.Dac2_Init) refers to stm32f10x_dac.o(i.DAC_Cmd) for DAC_Cmd
    dac.o(i.Dac2_Init) refers to stm32f10x_dac.o(i.DAC_SetChannel1Data) for DAC_SetChannel1Data
    exti.o(i.EXTIX_Init) refers to key.o(i.KEY_Init) for KEY_Init
    exti.o(i.EXTIX_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    exti.o(i.EXTIX_Init) refers to stm32f10x_gpio.o(i.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    exti.o(i.EXTIX_Init) refers to stm32f10x_exti.o(i.EXTI_Init) for EXTI_Init
    exti.o(i.EXTIX_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    dma.o(i.MYDMA1_Config) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    dma.o(i.MYDMA1_Config) refers to stm32f10x_dma.o(i.DMA_DeInit) for DMA_DeInit
    dma.o(i.MYDMA1_Config) refers to stm32f10x_dma.o(i.DMA_Init) for DMA_Init
    dma.o(i.MYDMA1_Config) refers to stm32f10x_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    dma.o(i.MYDMA1_Config) refers to misc.o(i.NVIC_Init) for NVIC_Init
    dma.o(i.MYDMA1_Config) refers to stm32f10x_dma.o(i.DMA_Cmd) for DMA_Cmd
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Clear) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Clear) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Color_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOff) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_DisplayOn) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(i.LCD_Scan_Dir) for LCD_Scan_Dir
    lcd.o(i.LCD_Display_Dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_DrawLine) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_DrawPoint) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_DrawRectangle) refers to lcd.o(i.LCD_DrawLine) for LCD_DrawLine
    lcd.o(i.LCD_Draw_Circle) refers to lcd.o(i.LCD_DrawPoint) for LCD_DrawPoint
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Fast_DrawPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Fill) refers to lcd.o(i.LCD_WriteRAM_Prepare) for LCD_WriteRAM_Prepare
    lcd.o(i.LCD_Fill) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    lcd.o(i.LCD_Init) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    lcd.o(i.LCD_Init) refers to _printf_hex_int.o(.text) for _printf_longlong_hex
    lcd.o(i.LCD_Init) refers to stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd) for RCC_AHBPeriphClockCmd
    lcd.o(i.LCD_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    lcd.o(i.LCD_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    lcd.o(i.LCD_Init) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMInit) for FSMC_NORSRAMInit
    lcd.o(i.LCD_Init) refers to stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd) for FSMC_NORSRAMCmd
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_ms) for delay_ms
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_ReadReg) for LCD_ReadReg
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_Init) refers to noretval__2printf.o(.text) for __2printf
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Init) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Init) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_SSD_BackLightSet) for LCD_SSD_BackLightSet
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Display_Dir) for LCD_Display_Dir
    lcd.o(i.LCD_Init) refers to lcd.o(i.LCD_Clear) for LCD_Clear
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.opt_delay) for opt_delay
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(i.LCD_BGR2RGB) for LCD_BGR2RGB
    lcd.o(i.LCD_ReadPoint) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_ReadReg) refers to delay.o(i.delay_us) for delay_us
    lcd.o(i.LCD_ReadReg) refers to lcd.o(i.LCD_RD_DATA) for LCD_RD_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SSD_BackLightSet) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SSD_BackLightSet) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    lcd.o(i.LCD_SSD_BackLightSet) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    lcd.o(i.LCD_SSD_BackLightSet) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Scan_Dir) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_SetCursor) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_SetCursor) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_REG) for LCD_WR_REG
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WR_DATA) for LCD_WR_DATA
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_WriteReg) for LCD_WriteReg
    lcd.o(i.LCD_Set_Window) refers to lcd.o(i.LCD_SetCursor) for LCD_SetCursor
    lcd.o(i.LCD_Set_Window) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowChar) refers to lcd.o(i.LCD_Fast_DrawPoint) for LCD_Fast_DrawPoint
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.constdata) for asc2_1206
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.data) for POINT_COLOR
    lcd.o(i.LCD_ShowChar) refers to lcd.o(.bss) for lcddev
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowString) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_Pow) for LCD_Pow
    lcd.o(i.LCD_ShowxNum) refers to lcd.o(i.LCD_ShowChar) for LCD_ShowChar
    lcd.o(i.LCD_WriteRAM_Prepare) refers to lcd.o(.bss) for lcddev
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_tim.o(i.TIM_OC2Init) for TIM_OC2Init
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_tim.o(i.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    timer.o(i.TIM2_PWM_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM3_Int_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM3_Int_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    timer.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    timer.o(i.TIM3_Int_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM4_Int_Init) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM4_Int_Init) refers to stm32f10x_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM4_Int_Init) refers to stm32f10x_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    timer.o(i.TIM4_Int_Init) refers to stm32f10x_tim.o(i.TIM_Cmd) for TIM_Cmd
    led.o(i.LED_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    led.o(i.LED_Init) refers to stm32f10x_gpio.o(i.GPIO_SetBits) for GPIO_SetBits
    key.o(i.KEY_Init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    key.o(i.KEY_Init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    key.o(i.KEY_Scan) refers to stm32f10x_gpio.o(i.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    key.o(i.KEY_Scan) refers to delay.o(i.delay_ms) for delay_ms
    key.o(i.KEY_Scan) refers to key.o(.data) for key_up
    delay.o(i.delay_init) refers to misc.o(i.SysTick_CLKSourceConfig) for SysTick_CLKSourceConfig
    delay.o(i.delay_init) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(i.delay_init) refers to delay.o(.data) for fac_us
    delay.o(i.delay_ms) refers to delay.o(.data) for fac_ms
    delay.o(i.delay_us) refers to delay.o(.data) for fac_us
    usart.o(i.USART1_IRQHandler) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f10x_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for USART_RX_STA
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(i._sys_exit) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.fputc) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(i.uart_init) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f10x_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to stm32f10x_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.DMA1_Channel1_IRQHandler) for DMA1_Channel1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to main.o(i.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(i.SystemInit) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_adc.o(i.ADC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(i.GPIO_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(i.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(i.USART_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(i.USART_Init) refers to stm32f10x_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(i.TIM_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI3_Config) for TI3_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TI4_Config) for TI4_Config
    stm32f10x_tim.o(i.TIM_ICInit) refers to stm32f10x_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_PWMIConfig) refers to stm32f10x_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI2_Config) for TI2_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TI1_Config) for TI1_Config
    stm32f10x_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f10x_dac.o(i.DAC_DeInit) refers to stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    __2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2sprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2sprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int.o(.text) for _printf_int_hex
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixu) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffixu.o(x$fpl$ffixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffixu.o(x$fpl$ffixur) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    frleqf.o(x$fpl$frleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frleqf.o(x$fpl$frleqf) refers to fleqf.o(x$fpl$fleqf) for __fpl_fcmple_InfNaN
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    sin.o(i.__softfp_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.sin) for sin
    sin.o(i.sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin.o(i.sin) refers to _rserrno.o(.text) for __set_errno
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.__sin$lsc) for __sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to _rserrno.o(.text) for __set_errno
    sin_x.o(i.__sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.__sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.__sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.__sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_noumaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_char_common.o(.text) refers to __printf_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(i.fputc) for fputc
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_noumaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    cos_i.o(i.__kernel_cos) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    rred.o(i.__ieee754_rem_pio2) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rred.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i.o(i.__kernel_sin) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(i._sys_exit) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(i._sys_exit) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f10x.o(i.SystemCoreClockUpdate), (164 bytes).
    Removing lcd.o(i.LCD_BGR2RGB), (26 bytes).
    Removing lcd.o(i.LCD_Color_Fill), (96 bytes).
    Removing lcd.o(i.LCD_DisplayOff), (96 bytes).
    Removing lcd.o(i.LCD_DisplayOn), (96 bytes).
    Removing lcd.o(i.LCD_DrawRectangle), (60 bytes).
    Removing lcd.o(i.LCD_Draw_Circle), (152 bytes).
    Removing lcd.o(i.LCD_Fill), (188 bytes).
    Removing lcd.o(i.LCD_ReadPoint), (364 bytes).
    Removing lcd.o(i.LCD_Set_Window), (532 bytes).
    Removing lcd.o(i.LCD_ShowxNum), (190 bytes).
    Removing lcd.o(i.LCD_WriteRAM), (12 bytes).
    Removing lcd.o(i.opt_delay), (14 bytes).
    Removing key.o(i.KEY_Scan), (200 bytes).
    Removing key.o(.data), (1 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(i.INTX_DISABLE), (4 bytes).
    Removing sys.o(i.INTX_ENABLE), (4 bytes).
    Removing sys.o(i.WFI_SET), (4 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (32 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogCmd), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_AutoInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(i.ADC_ClearITPendingBit), (10 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_DiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (16 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetITStatus), (36 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetInjectedConversionValue), (28 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartConvStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedChannelConfig), (130 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedDiscModeCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_InjectedSequencerLengthConfig), (24 bytes).
    Removing stm32f10x_adc.o(i.ADC_SetInjectedOffset), (20 bytes).
    Removing stm32f10x_adc.o(i.ADC_SoftwareStartInjectedConvCmd), (22 bytes).
    Removing stm32f10x_adc.o(i.ADC_StructInit), (18 bytes).
    Removing stm32f10x_adc.o(i.ADC_TempSensorVrefintCmd), (36 bytes).
    Removing stm32f10x_dma.o(i.DMA_ClearFlag), (28 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(i.DMA_GetFlagStatus), (44 bytes).
    Removing stm32f10x_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(i.DMA_StructInit), (26 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_AFIODeInit), (20 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_DeInit), (200 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputCmd), (12 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_EventOutputConfig), (32 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinLockConfig), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_PinRemapConfig), (144 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_ReadOutputDataBit), (18 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_StructInit), (16 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(i.GPIO_WriteBit), (10 bytes).
    Removing stm32f10x_rcc.o(i.RCC_APB1PeriphResetCmd), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_AdjustHSICalibrationValue), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearFlag), (20 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_DeInit), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetFlagStatus), (60 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetITStatus), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSEConfig), (76 bytes).
    Removing stm32f10x_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_ITConfig), (32 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSEConfig), (52 bytes).
    Removing stm32f10x_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK1Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PCLK2Config), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_PLLConfig), (28 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(i.RCC_SYSCLKConfig), (24 bytes).
    Removing stm32f10x_rcc.o(i.RCC_USBCLKConfig), (12 bytes).
    Removing stm32f10x_rcc.o(i.RCC_WaitForHSEStartUp), (56 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearFlag), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f10x_usart.o(i.USART_DMACmd), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_DeInit), (156 bytes).
    Removing stm32f10x_usart.o(i.USART_GetFlagStatus), (26 bytes).
    Removing stm32f10x_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_IrDAConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINBreakDetectLengthConfig), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_OverSampling8Cmd), (22 bytes).
    Removing stm32f10x_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(i.USART_SetAddress), (18 bytes).
    Removing stm32f10x_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_StructInit), (24 bytes).
    Removing stm32f10x_usart.o(i.USART_WakeUpConfig), (18 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearFlag), (64 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ClearITPendingBit), (72 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetECC), (28 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetFlagStatus), (56 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_GetITStatus), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_ITConfig), (128 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDDeInit), (68 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDECCCmd), (92 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDInit), (136 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NANDStructInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMDeInit), (54 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_NORSRAMStructInit), (114 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDCmd), (48 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDDeInit), (40 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDInit), (132 bytes).
    Removing stm32f10x_fsmc.o(i.FSMC_PCCARDStructInit), (60 bytes).
    Removing stm32f10x_tim.o(i.TI1_Config), (128 bytes).
    Removing stm32f10x_tim.o(i.TI2_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TI3_Config), (144 bytes).
    Removing stm32f10x_tim.o(i.TI4_Config), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRConfig), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_CCxNCmd), (30 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC1Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC2Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC3Ref), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ClearOC4Ref), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_CounterModeConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMACmd), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_DMAConfig), (10 bytes).
    Removing stm32f10x_tim.o(i.TIM_DeInit), (488 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode1Config), (54 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRClockMode2Config), (32 bytes).
    Removing stm32f10x_tim.o(i.TIM_ETRConfig), (28 bytes).
    Removing stm32f10x_tim.o(i.TIM_EncoderInterfaceConfig), (66 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC1Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC2Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC3Config), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ForcedOC4Config), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture1), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture2), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture3), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetCounter), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetFlagStatus), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_GetPrescaler), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICInit), (172 bytes).
    Removing stm32f10x_tim.o(i.TIM_ICStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_ITRxExternalClockConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_InternalClockConfig), (12 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1Init), (152 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1NPolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PolarityConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC1PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC2PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3FastConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3Init), (160 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3NPolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC3PreloadConfig), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4FastConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4Init), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PolarityConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OC4PreloadConfig), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f10x_tim.o(i.TIM_PWMIConfig), (124 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectInputTrigger), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectMasterSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOCxM), (82 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectOnePulseMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SelectSlaveMode), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetClockDivision), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCompare4), (6 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC1Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC2Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC3Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_SetIC4Prescaler), (26 bytes).
    Removing stm32f10x_tim.o(i.TIM_TIxExternalClockConfig), (62 bytes).
    Removing stm32f10x_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f10x_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f10x_dac.o(i.DAC_DMACmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_DeInit), (22 bytes).
    Removing stm32f10x_dac.o(i.DAC_DualSoftwareTriggerCmd), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(i.DAC_SetDualChannelData), (36 bytes).
    Removing stm32f10x_dac.o(i.DAC_SoftwareTriggerCmd), (44 bytes).
    Removing stm32f10x_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(i.DAC_WaveGenerationCmd), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetFlagStatus), (24 bytes).
    Removing stm32f10x_exti.o(i.EXTI_GetITStatus), (40 bytes).
    Removing stm32f10x_exti.o(i.EXTI_StructInit), (16 bytes).

230 unused section(s) (total 9947 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/dclz77c.s                  0x00000000   Number         0  __dclz77c.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2sprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_noumaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/ffixu.s                         0x00000000   Number         0  ffixu.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frleqf.s                        0x00000000   Number         0  frleqf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\ADC\adc.c                    0x00000000   Number         0  adc.o ABSOLUTE
    ..\HARDWARE\BEEP\beep.c                  0x00000000   Number         0  beep.o ABSOLUTE
    ..\HARDWARE\DAC\dac.c                    0x00000000   Number         0  dac.o ABSOLUTE
    ..\HARDWARE\DMA\dma.c                    0x00000000   Number         0  dma.o ABSOLUTE
    ..\HARDWARE\EXTI\exti.c                  0x00000000   Number         0  exti.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LCD\lcd.c                    0x00000000   Number         0  lcd.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\STM32F10x_DSP_Lib\src\cr4_fft_1024_stm32.s 0x00000000   Number         0  cr4_fft_1024_stm32.o ABSOLUTE
    ..\HARDWARE\TIMER\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_adc.c   0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dac.c   0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_dma.c   0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_exti.c  0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_fsmc.c  0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_tim.c   0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!dclz77c                                0x0800016c   Section      100  __dclz77c.o(!!dclz77c)
    !!handler_zi                             0x080001d0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001ec   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x080001ec   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001f2   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$00000017  0x080001f8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080001fc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080001fe   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080001fe   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000204   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000204   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800020e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800020e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000210   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x08000212   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x08000212   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000214   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000214   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000214   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800021a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800021a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800021e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800021e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000226   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000228   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000228   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800022c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000234   Section     4576  cr4_fft_1024_stm32.o(.text)
    .text                                    0x08001414   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08001454   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08001458   Section        0  noretval__2printf.o(.text)
    .text                                    0x08001470   Section        0  noretval__2sprintf.o(.text)
    .text                                    0x08001498   Section        0  _printf_hex_int.o(.text)
    .text                                    0x080014f0   Section        0  __printf_wp.o(.text)
    .text                                    0x080015fe   Section        0  heapauxi.o(.text)
    .text                                    0x08001604   Section        2  use_no_semi.o(.text)
    .text                                    0x08001606   Section        0  _rserrno.o(.text)
    .text                                    0x0800161c   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080016ce   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080016d1   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001aec   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001aed   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001b1c   Section        0  _sputc.o(.text)
    .text                                    0x08001b28   Section        0  _printf_char_file.o(.text)
    .text                                    0x08001b4c   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001b54   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08001b5c   Section      138  lludiv10.o(.text)
    .text                                    0x08001be8   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001c68   Section        0  bigflt0.o(.text)
    .text                                    0x08001d4c   Section        0  ferror.o(.text)
    .text                                    0x08001d54   Section        8  libspace.o(.text)
    .text                                    0x08001d5c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001da6   Section        0  exit.o(.text)
    .text                                    0x08001db8   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001e38   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001e76   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001ebc   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08001f1c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002254   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002330   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x0800235a   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08002384   Section      580  btod.o(CL$$btod_mult_common)
    i.ADC_Cmd                                0x080025c8   Section        0  stm32f10x_adc.o(i.ADC_Cmd)
    i.ADC_DMACmd                             0x080025de   Section        0  stm32f10x_adc.o(i.ADC_DMACmd)
    i.ADC_DeInit                             0x080025f4   Section        0  stm32f10x_adc.o(i.ADC_DeInit)
    i.ADC_GetCalibrationStatus               0x08002650   Section        0  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    i.ADC_GetResetCalibrationStatus          0x08002664   Section        0  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    i.ADC_Init                               0x08002678   Section        0  stm32f10x_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080026c8   Section        0  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    i.ADC_ResetCalibration                   0x08002780   Section        0  stm32f10x_adc.o(i.ADC_ResetCalibration)
    i.ADC_SoftwareStartConvCmd               0x0800278a   Section        0  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    i.ADC_StartCalibration                   0x080027a0   Section        0  stm32f10x_adc.o(i.ADC_StartCalibration)
    i.Adc_Init                               0x080027ac   Section        0  adc.o(i.Adc_Init)
    i.BEEP_Init                              0x08002860   Section        0  beep.o(i.BEEP_Init)
    i.BusFault_Handler                       0x08002898   Section        0  stm32f10x_it.o(i.BusFault_Handler)
    i.DAC_Cmd                                0x0800289c   Section        0  stm32f10x_dac.o(i.DAC_Cmd)
    i.DAC_Init                               0x080028c4   Section        0  stm32f10x_dac.o(i.DAC_Init)
    i.DAC_SetChannel1Data                    0x080028f8   Section        0  stm32f10x_dac.o(i.DAC_SetChannel1Data)
    i.DMA1_Channel1_IRQHandler               0x08002918   Section        0  main.o(i.DMA1_Channel1_IRQHandler)
    i.DMA_ClearITPendingBit                  0x08002974   Section        0  stm32f10x_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x08002990   Section        0  stm32f10x_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x080029a8   Section        0  stm32f10x_dma.o(i.DMA_DeInit)
    i.DMA_GetITStatus                        0x08002af4   Section        0  stm32f10x_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x08002b20   Section        0  stm32f10x_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08002b32   Section        0  stm32f10x_dma.o(i.DMA_Init)
    i.Dac1_Init                              0x08002b70   Section        0  dac.o(i.Dac1_Init)
    i.Dac2_Init                              0x08002bcc   Section        0  dac.o(i.Dac2_Init)
    i.DebugMon_Handler                       0x08002c2c   Section        0  stm32f10x_it.o(i.DebugMon_Handler)
    i.EXTI0_IRQHandler                       0x08002c30   Section        0  main.o(i.EXTI0_IRQHandler)
    i.EXTI3_IRQHandler                       0x08002cd0   Section        0  main.o(i.EXTI3_IRQHandler)
    i.EXTI4_IRQHandler                       0x08002d34   Section        0  main.o(i.EXTI4_IRQHandler)
    i.EXTIX_Init                             0x08002d98   Section        0  exti.o(i.EXTIX_Init)
    i.EXTI_ClearITPendingBit                 0x08002e48   Section        0  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    i.EXTI_Init                              0x08002e54   Section        0  stm32f10x_exti.o(i.EXTI_Init)
    i.FSMC_NORSRAMCmd                        0x08002ee8   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    i.FSMC_NORSRAMInit                       0x08002f1c   Section        0  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    i.GPIO_EXTILineConfig                    0x08003004   Section        0  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    i.GPIO_Init                              0x08003044   Section        0  stm32f10x_gpio.o(i.GPIO_Init)
    i.GPIO_ReadInputDataBit                  0x0800315a   Section        0  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    i.GPIO_ResetBits                         0x0800316c   Section        0  stm32f10x_gpio.o(i.GPIO_ResetBits)
    i.GPIO_SetBits                           0x08003170   Section        0  stm32f10x_gpio.o(i.GPIO_SetBits)
    i.GetPowerMag                            0x08003174   Section        0  main.o(i.GetPowerMag)
    i.HardFault_Handler                      0x08003294   Section        0  stm32f10x_it.o(i.HardFault_Handler)
    i.InitBufInArray                         0x08003298   Section        0  main.o(i.InitBufInArray)
    i.KEY_Init                               0x08003310   Section        0  key.o(i.KEY_Init)
    i.LCD_Clear                              0x0800334c   Section        0  lcd.o(i.LCD_Clear)
    i.LCD_Display_Dir                        0x080033b8   Section        0  lcd.o(i.LCD_Display_Dir)
    i.LCD_DrawLine                           0x08003578   Section        0  lcd.o(i.LCD_DrawLine)
    i.LCD_DrawPoint                          0x08003628   Section        0  lcd.o(i.LCD_DrawPoint)
    i.LCD_Fast_DrawPoint                     0x0800364c   Section        0  lcd.o(i.LCD_Fast_DrawPoint)
    i.LCD_Init                               0x080037c8   Section        0  lcd.o(i.LCD_Init)
    i.LCD_Pow                                0x08006f34   Section        0  lcd.o(i.LCD_Pow)
    i.LCD_RD_DATA                            0x08006f4c   Section        0  lcd.o(i.LCD_RD_DATA)
    i.LCD_ReadReg                            0x08006f60   Section        0  lcd.o(i.LCD_ReadReg)
    i.LCD_SSD_BackLightSet                   0x08006f78   Section        0  lcd.o(i.LCD_SSD_BackLightSet)
    i.LCD_Scan_Dir                           0x08006fcc   Section        0  lcd.o(i.LCD_Scan_Dir)
    i.LCD_SetCursor                          0x080072b8   Section        0  lcd.o(i.LCD_SetCursor)
    i.LCD_ShowChar                           0x08007460   Section        0  lcd.o(i.LCD_ShowChar)
    i.LCD_ShowNum                            0x08007588   Section        0  lcd.o(i.LCD_ShowNum)
    i.LCD_ShowString                         0x0800761c   Section        0  lcd.o(i.LCD_ShowString)
    i.LCD_WR_DATA                            0x08007684   Section        0  lcd.o(i.LCD_WR_DATA)
    i.LCD_WR_REG                             0x08007690   Section        0  lcd.o(i.LCD_WR_REG)
    i.LCD_WriteRAM_Prepare                   0x0800769c   Section        0  lcd.o(i.LCD_WriteRAM_Prepare)
    i.LCD_WriteReg                           0x080076b0   Section        0  lcd.o(i.LCD_WriteReg)
    i.LED_Init                               0x080076c0   Section        0  led.o(i.LED_Init)
    i.MYDMA1_Config                          0x0800770c   Section        0  dma.o(i.MYDMA1_Config)
    i.MemManage_Handler                      0x08007788   Section        0  stm32f10x_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x0800778c   Section        0  stm32f10x_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08007790   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08007800   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08007814   Section        0  stm32f10x_it.o(i.PendSV_Handler)
    i.RCC_ADCCLKConfig                       0x08007818   Section        0  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    i.RCC_AHBPeriphClockCmd                  0x08007830   Section        0  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08007850   Section        0  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08007870   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08007890   Section        0  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x080078b0   Section        0  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08007984   Section        0  stm32f10x_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08007986   Section        0  system_stm32f10x.o(i.SetSysClock)
    SetSysClock                              0x08007987   Thumb Code     8  system_stm32f10x.o(i.SetSysClock)
    i.SetSysClockTo72                        0x08007990   Section        0  system_stm32f10x.o(i.SetSysClockTo72)
    SetSysClockTo72                          0x08007991   Thumb Code   214  system_stm32f10x.o(i.SetSysClockTo72)
    i.SysTick_CLKSourceConfig                0x08007a70   Section        0  misc.o(i.SysTick_CLKSourceConfig)
    i.SysTick_Handler                        0x08007a98   Section        0  stm32f10x_it.o(i.SysTick_Handler)
    i.SystemInit                             0x08007a9c   Section        0  system_stm32f10x.o(i.SystemInit)
    i.TIM2_PWM_Init                          0x08007afc   Section        0  timer.o(i.TIM2_PWM_Init)
    i.TIM3_IRQHandler                        0x08007b84   Section        0  main.o(i.TIM3_IRQHandler)
    i.TIM3_Int_Init                          0x08007ba4   Section        0  timer.o(i.TIM3_Int_Init)
    i.TIM4_Int_Init                          0x08007c0c   Section        0  timer.o(i.TIM4_Int_Init)
    i.TIM_ClearITPendingBit                  0x08007c4c   Section        0  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    i.TIM_Cmd                                0x08007c52   Section        0  stm32f10x_tim.o(i.TIM_Cmd)
    i.TIM_CtrlPWMOutputs                     0x08007c6a   Section        0  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    i.TIM_GetITStatus                        0x08007c88   Section        0  stm32f10x_tim.o(i.TIM_GetITStatus)
    i.TIM_ITConfig                           0x08007caa   Section        0  stm32f10x_tim.o(i.TIM_ITConfig)
    i.TIM_OC2Init                            0x08007cbc   Section        0  stm32f10x_tim.o(i.TIM_OC2Init)
    i.TIM_PrescalerConfig                    0x08007d60   Section        0  stm32f10x_tim.o(i.TIM_PrescalerConfig)
    i.TIM_SelectOutputTrigger                0x08007d66   Section        0  stm32f10x_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_TimeBaseInit                       0x08007d78   Section        0  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    i.USART1_IRQHandler                      0x08007e1c   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x08007ea4   Section        0  stm32f10x_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x08007ebc   Section        0  stm32f10x_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08007f10   Section        0  stm32f10x_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x08007f5c   Section        0  stm32f10x_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x08008034   Section        0  stm32f10x_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x0800803e   Section        0  stm32f10x_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08008042   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ieee754_rem_pio2                     0x0800806c   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x080083f4   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x080084f0   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x0800859c   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x08008688   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_invalid                  0x0800868e   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x0800869c   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x080086ac   Section        0  __printf_wp.o(i._is_digit)
    i._sys_exit                              0x080086ba   Section        0  usart.o(i._sys_exit)
    i.clear_point                            0x080086c0   Section        0  main.o(i.clear_point)
    i.delay_init                             0x08008838   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x0800887c   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x080088c8   Section        0  delay.o(i.delay_us)
    i.fputc                                  0x08008914   Section        0  usart.o(i.fputc)
    i.lcd_huadian                            0x08008930   Section        0  main.o(i.lcd_huadian)
    i.lcd_huaxian                            0x08008948   Section        0  main.o(i.lcd_huaxian)
    i.main                                   0x0800896c   Section        0  main.o(i.main)
    i.sin                                    0x08008bb8   Section        0  sin.o(i.sin)
    i.sinout                                 0x08008c58   Section        0  main.o(i.sinout)
    i.sqrt                                   0x08008c8c   Section        0  sqrt.o(i.sqrt)
    i.uart_init                              0x08008cd8   Section        0  usart.o(i.uart_init)
    i.window                                 0x08008d78   Section        0  main.o(i.window)
    locale$$code                             0x08008fbc   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x08008fe8   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x0800904c   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x0800905d   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x0800919c   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$ddiv                               0x080091ac   Section      688  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x080091b3   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x0800945c   Section       94  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x080094bc   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x08009516   Section       46  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08009544   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dmul                               0x0800956c   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x080096c0   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x0800975c   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drsb                               0x08009768   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08009780   Section      460  dsqrt_noumaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x0800994c   Section      468  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800995d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08009b20   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08009b78   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x08009b87   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmpinf                            0x08009c3c   Section       24  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$ffixu                              0x08009c54   Section       62  ffixu.o(x$fpl$ffixu)
    x$fpl$fflt                               0x08009c94   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fleqf                              0x08009cc4   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08009d2c   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08009e2e   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08009eba   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$frleqf                             0x08009ec4   Section       98  frleqf.o(x$fpl$frleqf)
    x$fpl$fsub                               0x08009f28   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08009f37   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$printf1                            0x0800a012   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x0800a016   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800a07a   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x0800a0d6   Section       48  trapv.o(x$fpl$trapveneer)
    .constdata                               0x0800a106   Section     6080  lcd.o(.constdata)
    x$fpl$usenofp                            0x0800a106   Section        0  usenofp.o(x$fpl$usenofp)
    .constdata                               0x0800b8c6   Section       40  _printf_hex_int.o(.constdata)
    uc_hextab                                0x0800b8c6   Data          20  _printf_hex_int.o(.constdata)
    lc_hextab                                0x0800b8da   Data          20  _printf_hex_int.o(.constdata)
    .constdata                               0x0800b8f0   Section       48  cos_i.o(.constdata)
    C                                        0x0800b8f0   Data          48  cos_i.o(.constdata)
    .constdata                               0x0800b920   Section      200  rred.o(.constdata)
    pio2s                                    0x0800b920   Data          48  rred.o(.constdata)
    twooverpi                                0x0800b950   Data         152  rred.o(.constdata)
    .constdata                               0x0800b9e8   Section       40  sin_i.o(.constdata)
    S                                        0x0800b9e8   Data          40  sin_i.o(.constdata)
    .constdata                               0x0800ba10   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800ba10   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800ba4c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800bac4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800bac8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800bad0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800badc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800bade   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800badf   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0800bae0   Data           0  lc_numeric_c.o(locale$$data)
    .data                                    0x20000000   Section     4150  main.o(.data)
    h                                        0x20001030   Data           2  main.o(.data)
    h                                        0x20001032   Data           2  main.o(.data)
    i                                        0x20001034   Data           2  main.o(.data)
    .data                                    0x20001038   Section       20  system_stm32f10x.o(.data)
    .data                                    0x2000104c   Section        4  lcd.o(.data)
    .data                                    0x20001050   Section        4  delay.o(.data)
    fac_us                                   0x20001050   Data           1  delay.o(.data)
    fac_ms                                   0x20001052   Data           2  delay.o(.data)
    .data                                    0x20001054   Section        6  usart.o(.data)
    .data                                    0x2000105a   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x2000105a   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x2000106a   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20001070   Section    14336  main.o(.bss)
    .bss                                     0x20004870   Section       14  lcd.o(.bss)
    .bss                                     0x2000487e   Section      200  usart.o(.bss)
    .bss                                     0x20004948   Section       96  libspace.o(.bss)
    HEAP                                     0x200049a8   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x200049a8   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20004ba8   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20004ba8   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20004fa8   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_ctype                             - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_post_padding                      - Undefined Weak Reference
    _printf_pre_padding                       - Undefined Weak Reference
    _printf_truncate_unsigned                 - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x0800016d   Thumb Code   100  __dclz77c.o(!!dclz77c)
    __decompress2                            0x0800016d   Thumb Code     0  __dclz77c.o(!!dclz77c)
    __scatterload_zeroinit                   0x080001d1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x080001ed   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x080001ed   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_x                                0x080001f3   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_percent_end                      0x080001f9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080001fd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080001ff   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000205   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800020f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000211   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x08000213   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000215   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000215   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000215   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800021b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800021f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000227   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000229   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800022d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    cr4_fft_1024_stm32                       0x08000235   Thumb Code     0  cr4_fft_1024_stm32.o(.text)
    Reset_Handler                            0x08001415   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART2_IRQHandler                        0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x0800142f   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08001431   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __use_no_semihosting                     0x08001455   Thumb Code     2  use_no_semi_2.o(.text)
    __2printf                                0x08001459   Thumb Code    20  noretval__2printf.o(.text)
    __2sprintf                               0x08001471   Thumb Code    34  noretval__2sprintf.o(.text)
    _printf_int_hex                          0x08001499   Thumb Code    84  _printf_hex_int.o(.text)
    _printf_longlong_hex                     0x08001499   Thumb Code     0  _printf_hex_int.o(.text)
    __printf                                 0x080014f1   Thumb Code   270  __printf_wp.o(.text)
    __use_two_region_memory                  0x080015ff   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08001601   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08001603   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08001605   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001605   Thumb Code     2  use_no_semi.o(.text)
    __read_errno                             0x08001607   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08001611   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800161d   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x080016cf   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08001881   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08001af7   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08001b1d   Thumb Code    10  _sputc.o(.text)
    _printf_char_file                        0x08001b29   Thumb Code    32  _printf_char_file.o(.text)
    __rt_locale                              0x08001b4d   Thumb Code     8  rt_locale_intlibspace.o(.text)
    __aeabi_errno_addr                       0x08001b55   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08001b55   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08001b55   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08001b5d   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08001be9   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001c69   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08001d4d   Thumb Code     8  ferror.o(.text)
    __user_libspace                          0x08001d55   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001d55   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001d55   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001d5d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x08001da7   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001db9   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001e39   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001e77   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001ebd   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08001f1d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002255   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002331   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x0800235b   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08002385   Thumb Code   580  btod.o(CL$$btod_mult_common)
    ADC_Cmd                                  0x080025c9   Thumb Code    22  stm32f10x_adc.o(i.ADC_Cmd)
    ADC_DMACmd                               0x080025df   Thumb Code    22  stm32f10x_adc.o(i.ADC_DMACmd)
    ADC_DeInit                               0x080025f5   Thumb Code    78  stm32f10x_adc.o(i.ADC_DeInit)
    ADC_GetCalibrationStatus                 0x08002651   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetCalibrationStatus)
    ADC_GetResetCalibrationStatus            0x08002665   Thumb Code    20  stm32f10x_adc.o(i.ADC_GetResetCalibrationStatus)
    ADC_Init                                 0x08002679   Thumb Code    70  stm32f10x_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080026c9   Thumb Code   184  stm32f10x_adc.o(i.ADC_RegularChannelConfig)
    ADC_ResetCalibration                     0x08002781   Thumb Code    10  stm32f10x_adc.o(i.ADC_ResetCalibration)
    ADC_SoftwareStartConvCmd                 0x0800278b   Thumb Code    22  stm32f10x_adc.o(i.ADC_SoftwareStartConvCmd)
    ADC_StartCalibration                     0x080027a1   Thumb Code    10  stm32f10x_adc.o(i.ADC_StartCalibration)
    Adc_Init                                 0x080027ad   Thumb Code   172  adc.o(i.Adc_Init)
    BEEP_Init                                0x08002861   Thumb Code    50  beep.o(i.BEEP_Init)
    BusFault_Handler                         0x08002899   Thumb Code     4  stm32f10x_it.o(i.BusFault_Handler)
    DAC_Cmd                                  0x0800289d   Thumb Code    34  stm32f10x_dac.o(i.DAC_Cmd)
    DAC_Init                                 0x080028c5   Thumb Code    46  stm32f10x_dac.o(i.DAC_Init)
    DAC_SetChannel1Data                      0x080028f9   Thumb Code    26  stm32f10x_dac.o(i.DAC_SetChannel1Data)
    DMA1_Channel1_IRQHandler                 0x08002919   Thumb Code    70  main.o(i.DMA1_Channel1_IRQHandler)
    DMA_ClearITPendingBit                    0x08002975   Thumb Code    18  stm32f10x_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x08002991   Thumb Code    24  stm32f10x_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x080029a9   Thumb Code   324  stm32f10x_dma.o(i.DMA_DeInit)
    DMA_GetITStatus                          0x08002af5   Thumb Code    36  stm32f10x_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x08002b21   Thumb Code    18  stm32f10x_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08002b33   Thumb Code    60  stm32f10x_dma.o(i.DMA_Init)
    Dac1_Init                                0x08002b71   Thumb Code    86  dac.o(i.Dac1_Init)
    Dac2_Init                                0x08002bcd   Thumb Code    92  dac.o(i.Dac2_Init)
    DebugMon_Handler                         0x08002c2d   Thumb Code     2  stm32f10x_it.o(i.DebugMon_Handler)
    EXTI0_IRQHandler                         0x08002c31   Thumb Code   122  main.o(i.EXTI0_IRQHandler)
    EXTI3_IRQHandler                         0x08002cd1   Thumb Code    84  main.o(i.EXTI3_IRQHandler)
    EXTI4_IRQHandler                         0x08002d35   Thumb Code    84  main.o(i.EXTI4_IRQHandler)
    EXTIX_Init                               0x08002d99   Thumb Code   176  exti.o(i.EXTIX_Init)
    EXTI_ClearITPendingBit                   0x08002e49   Thumb Code     6  stm32f10x_exti.o(i.EXTI_ClearITPendingBit)
    EXTI_Init                                0x08002e55   Thumb Code   142  stm32f10x_exti.o(i.EXTI_Init)
    FSMC_NORSRAMCmd                          0x08002ee9   Thumb Code    46  stm32f10x_fsmc.o(i.FSMC_NORSRAMCmd)
    FSMC_NORSRAMInit                         0x08002f1d   Thumb Code   230  stm32f10x_fsmc.o(i.FSMC_NORSRAMInit)
    GPIO_EXTILineConfig                      0x08003005   Thumb Code    60  stm32f10x_gpio.o(i.GPIO_EXTILineConfig)
    GPIO_Init                                0x08003045   Thumb Code   278  stm32f10x_gpio.o(i.GPIO_Init)
    GPIO_ReadInputDataBit                    0x0800315b   Thumb Code    18  stm32f10x_gpio.o(i.GPIO_ReadInputDataBit)
    GPIO_ResetBits                           0x0800316d   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_ResetBits)
    GPIO_SetBits                             0x08003171   Thumb Code     4  stm32f10x_gpio.o(i.GPIO_SetBits)
    GetPowerMag                              0x08003175   Thumb Code   248  main.o(i.GetPowerMag)
    HardFault_Handler                        0x08003295   Thumb Code     4  stm32f10x_it.o(i.HardFault_Handler)
    InitBufInArray                           0x08003299   Thumb Code   100  main.o(i.InitBufInArray)
    KEY_Init                                 0x08003311   Thumb Code    52  key.o(i.KEY_Init)
    LCD_Clear                                0x0800334d   Thumb Code   100  lcd.o(i.LCD_Clear)
    LCD_Display_Dir                          0x080033b9   Thumb Code   444  lcd.o(i.LCD_Display_Dir)
    LCD_DrawLine                             0x08003579   Thumb Code   176  lcd.o(i.LCD_DrawLine)
    LCD_DrawPoint                            0x08003629   Thumb Code    28  lcd.o(i.LCD_DrawPoint)
    LCD_Fast_DrawPoint                       0x0800364d   Thumb Code   370  lcd.o(i.LCD_Fast_DrawPoint)
    LCD_Init                                 0x080037c9   Thumb Code 14178  lcd.o(i.LCD_Init)
    LCD_Pow                                  0x08006f35   Thumb Code    22  lcd.o(i.LCD_Pow)
    LCD_RD_DATA                              0x08006f4d   Thumb Code    14  lcd.o(i.LCD_RD_DATA)
    LCD_ReadReg                              0x08006f61   Thumb Code    22  lcd.o(i.LCD_ReadReg)
    LCD_SSD_BackLightSet                     0x08006f79   Thumb Code    80  lcd.o(i.LCD_SSD_BackLightSet)
    LCD_Scan_Dir                             0x08006fcd   Thumb Code   744  lcd.o(i.LCD_Scan_Dir)
    LCD_SetCursor                            0x080072b9   Thumb Code   418  lcd.o(i.LCD_SetCursor)
    LCD_ShowChar                             0x08007461   Thumb Code   272  lcd.o(i.LCD_ShowChar)
    LCD_ShowNum                              0x08007589   Thumb Code   148  lcd.o(i.LCD_ShowNum)
    LCD_ShowString                           0x0800761d   Thumb Code   102  lcd.o(i.LCD_ShowString)
    LCD_WR_DATA                              0x08007685   Thumb Code     6  lcd.o(i.LCD_WR_DATA)
    LCD_WR_REG                               0x08007691   Thumb Code     6  lcd.o(i.LCD_WR_REG)
    LCD_WriteRAM_Prepare                     0x0800769d   Thumb Code    10  lcd.o(i.LCD_WriteRAM_Prepare)
    LCD_WriteReg                             0x080076b1   Thumb Code    10  lcd.o(i.LCD_WriteReg)
    LED_Init                                 0x080076c1   Thumb Code    68  led.o(i.LED_Init)
    MYDMA1_Config                            0x0800770d   Thumb Code   120  dma.o(i.MYDMA1_Config)
    MemManage_Handler                        0x08007789   Thumb Code     4  stm32f10x_it.o(i.MemManage_Handler)
    NMI_Handler                              0x0800778d   Thumb Code     2  stm32f10x_it.o(i.NMI_Handler)
    NVIC_Init                                0x08007791   Thumb Code   100  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08007801   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08007815   Thumb Code     2  stm32f10x_it.o(i.PendSV_Handler)
    RCC_ADCCLKConfig                         0x08007819   Thumb Code    18  stm32f10x_rcc.o(i.RCC_ADCCLKConfig)
    RCC_AHBPeriphClockCmd                    0x08007831   Thumb Code    26  stm32f10x_rcc.o(i.RCC_AHBPeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08007851   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08007871   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08007891   Thumb Code    26  stm32f10x_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x080078b1   Thumb Code   192  stm32f10x_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08007985   Thumb Code     2  stm32f10x_it.o(i.SVC_Handler)
    SysTick_CLKSourceConfig                  0x08007a71   Thumb Code    40  misc.o(i.SysTick_CLKSourceConfig)
    SysTick_Handler                          0x08007a99   Thumb Code     2  stm32f10x_it.o(i.SysTick_Handler)
    SystemInit                               0x08007a9d   Thumb Code    78  system_stm32f10x.o(i.SystemInit)
    TIM2_PWM_Init                            0x08007afd   Thumb Code   132  timer.o(i.TIM2_PWM_Init)
    TIM3_IRQHandler                          0x08007b85   Thumb Code    26  main.o(i.TIM3_IRQHandler)
    TIM3_Int_Init                            0x08007ba5   Thumb Code    98  timer.o(i.TIM3_Int_Init)
    TIM4_Int_Init                            0x08007c0d   Thumb Code    58  timer.o(i.TIM4_Int_Init)
    TIM_ClearITPendingBit                    0x08007c4d   Thumb Code     6  stm32f10x_tim.o(i.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08007c53   Thumb Code    24  stm32f10x_tim.o(i.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08007c6b   Thumb Code    30  stm32f10x_tim.o(i.TIM_CtrlPWMOutputs)
    TIM_GetITStatus                          0x08007c89   Thumb Code    34  stm32f10x_tim.o(i.TIM_GetITStatus)
    TIM_ITConfig                             0x08007cab   Thumb Code    18  stm32f10x_tim.o(i.TIM_ITConfig)
    TIM_OC2Init                              0x08007cbd   Thumb Code   154  stm32f10x_tim.o(i.TIM_OC2Init)
    TIM_PrescalerConfig                      0x08007d61   Thumb Code     6  stm32f10x_tim.o(i.TIM_PrescalerConfig)
    TIM_SelectOutputTrigger                  0x08007d67   Thumb Code    18  stm32f10x_tim.o(i.TIM_SelectOutputTrigger)
    TIM_TimeBaseInit                         0x08007d79   Thumb Code   122  stm32f10x_tim.o(i.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08007e1d   Thumb Code   122  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x08007ea5   Thumb Code    24  stm32f10x_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x08007ebd   Thumb Code    84  stm32f10x_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08007f11   Thumb Code    74  stm32f10x_usart.o(i.USART_ITConfig)
    USART_Init                               0x08007f5d   Thumb Code   210  stm32f10x_usart.o(i.USART_Init)
    USART_ReceiveData                        0x08008035   Thumb Code    10  stm32f10x_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x0800803f   Thumb Code     4  stm32f10x_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08008043   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __ieee754_rem_pio2                       0x0800806d   Thumb Code   828  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x080083f5   Thumb Code   230  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x080084f1   Thumb Code   170  poly.o(i.__kernel_poly)
    __kernel_sin                             0x0800859d   Thumb Code   224  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x08008689   Thumb Code     6  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_invalid                    0x0800868f   Thumb Code    12  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x0800869d   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x080086ad   Thumb Code    14  __printf_wp.o(i._is_digit)
    _sys_exit                                0x080086bb   Thumb Code     6  usart.o(i._sys_exit)
    clear_point                              0x080086c1   Thumb Code   330  main.o(i.clear_point)
    delay_init                               0x08008839   Thumb Code    50  delay.o(i.delay_init)
    delay_ms                                 0x0800887d   Thumb Code    72  delay.o(i.delay_ms)
    delay_us                                 0x080088c9   Thumb Code    72  delay.o(i.delay_us)
    fputc                                    0x08008915   Thumb Code    24  usart.o(i.fputc)
    lcd_huadian                              0x08008931   Thumb Code    24  main.o(i.lcd_huadian)
    lcd_huaxian                              0x08008949   Thumb Code    36  main.o(i.lcd_huaxian)
    main                                     0x0800896d   Thumb Code   520  main.o(i.main)
    sin                                      0x08008bb9   Thumb Code   150  sin.o(i.sin)
    sinout                                   0x08008c59   Thumb Code    44  main.o(i.sinout)
    sqrt                                     0x08008c8d   Thumb Code    76  sqrt.o(i.sqrt)
    uart_init                                0x08008cd9   Thumb Code   152  usart.o(i.uart_init)
    window                                   0x08008d79   Thumb Code   474  main.o(i.window)
    _get_lc_numeric                          0x08008fbd   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x08008fe9   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08008fe9   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x0800904d   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x0800904d   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x0800919d   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __aeabi_ddiv                             0x080091ad   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x080091ad   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x0800945d   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x0800945d   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x080094bd   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x080094bd   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x08009517   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08009517   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08009545   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08009545   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_dmul                             0x0800956d   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x0800956d   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x080096c1   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x0800975d   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_drsub                            0x08009769   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x08009769   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08009781   Thumb Code   456  dsqrt_noumaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x0800994d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x0800994d   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08009b21   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08009b21   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08009b79   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08009b79   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __fpl_fcmp_Inf                           0x08009c3d   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_f2uiz                            0x08009c55   Thumb Code     0  ffixu.o(x$fpl$ffixu)
    _ffixu                                   0x08009c55   Thumb Code    62  ffixu.o(x$fpl$ffixu)
    __aeabi_i2f                              0x08009c95   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08009c95   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_cfcmple                          0x08009cc5   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08009cc5   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08009d17   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08009d2d   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08009d2d   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08009e2f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08009ebb   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_cfrcmple                         0x08009ec5   Thumb Code     0  frleqf.o(x$fpl$frleqf)
    _frcmple                                 0x08009ec5   Thumb Code    98  frleqf.o(x$fpl$frleqf)
    __aeabi_fsub                             0x08009f29   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08009f29   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    _printf_fp_dec                           0x0800a013   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x0800a017   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800a07b   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x0800a0d7   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x0800a106   Number         0  usenofp.o(x$fpl$usenofp)
    asc2_1206                                0x0800a106   Data        1140  lcd.o(.constdata)
    asc2_1608                                0x0800a57a   Data        1520  lcd.o(.constdata)
    asc2_2412                                0x0800ab6a   Data        3420  lcd.o(.constdata)
    Region$$Table$$Base                      0x0800baa4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800bac4   Number         0  anon$$obj.o(Region$$Table)
    TableFFT                                 0x20000000   Data        4080  main.o(.data)
    table                                    0x20000ff0   Data          30  main.o(.data)
    currentadc                               0x2000100e   Data           2  main.o(.data)
    adcmax                                   0x20001010   Data           4  main.o(.data)
    adcmin                                   0x20001014   Data           4  main.o(.data)
    adc_flag                                 0x20001018   Data           1  main.o(.data)
    key_flag                                 0x20001019   Data           1  main.o(.data)
    show_flag                                0x2000101a   Data           1  main.o(.data)
    T                                        0x2000101c   Data           2  main.o(.data)
    pre                                      0x2000101e   Data           2  main.o(.data)
    fre                                      0x20001020   Data           4  main.o(.data)
    F                                        0x20001024   Data           4  main.o(.data)
    V                                        0x20001028   Data           2  main.o(.data)
    temp                                     0x2000102a   Data           2  main.o(.data)
    t                                        0x2000102c   Data           2  main.o(.data)
    key                                      0x2000102e   Data           2  main.o(.data)
    SystemCoreClock                          0x20001038   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x2000103c   Data          16  system_stm32f10x.o(.data)
    POINT_COLOR                              0x2000104c   Data           2  lcd.o(.data)
    BACK_COLOR                               0x2000104e   Data           2  lcd.o(.data)
    __stdout                                 0x20001054   Data           4  usart.o(.data)
    USART_RX_STA                             0x20001058   Data           2  usart.o(.data)
    fftin                                    0x20001070   Data        4096  main.o(.bss)
    fftout                                   0x20002070   Data        4096  main.o(.bss)
    FFT_Mag                                  0x20003070   Data        2048  main.o(.bss)
    magout                                   0x20003870   Data        2048  main.o(.bss)
    adcx                                     0x20004070   Data        2048  main.o(.bss)
    lcddev                                   0x20004870   Data          14  lcd.o(.bss)
    USART_RX_BUF                             0x2000487e   Data         200  usart.o(.bss)
    __libspace_start                         0x20004948   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200049a8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000cb50, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000c730])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000bae0, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          800    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO         2489  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO         2838    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x00000064   Code   RO         2836    !!dclz77c           c_w.l(__dclz77c.o)
    0x080001d0   0x080001d0   0x0000001c   Code   RO         2840    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001ec   0x080001ec   0x00000000   Code   RO         2486    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001ec   0x080001ec   0x00000006   Code   RO         2485    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001f2   0x080001f2   0x00000006   Code   RO         2484    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001f8   0x080001f8   0x00000004   Code   RO         2570    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080001fc   0x080001fc   0x00000002   Code   RO         2707    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         2709    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         2711    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         2714    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         2716    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000000   Code   RO         2718    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080001fe   0x080001fe   0x00000006   Code   RO         2719    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000000   Code   RO         2721    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000000   Code   RO         2723    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x00000000   Code   RO         2725    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000204   0x08000204   0x0000000a   Code   RO         2726    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2727    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2729    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2731    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2733    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2735    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2737    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2739    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2741    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2745    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2747    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2749    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000000   Code   RO         2751    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800020e   0x0800020e   0x00000002   Code   RO         2752    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000210   0x08000210   0x00000002   Code   RO         2786    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2795    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2797    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2800    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2803    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2805    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000000   Code   RO         2808    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x08000212   0x08000212   0x00000002   Code   RO         2809    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000214   0x08000214   0x00000000   Code   RO         2557    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000214   0x08000214   0x00000000   Code   RO         2615    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000214   0x08000214   0x00000006   Code   RO         2627    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800021a   0x0800021a   0x00000000   Code   RO         2617    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800021a   0x0800021a   0x00000004   Code   RO         2618    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         2620    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800021e   0x0800021e   0x00000008   Code   RO         2621    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000226   0x08000226   0x00000002   Code   RO         2755    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000228   0x08000228   0x00000000   Code   RO         2764    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000228   0x08000228   0x00000004   Code   RO         2765    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800022c   0x0800022c   0x00000006   Code   RO         2766    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000232   0x08000232   0x00000002   PAD
    0x08000234   0x08000234   0x000011e0   Code   RO          689    .text               cr4_fft_1024_stm32.o
    0x08001414   0x08001414   0x00000040   Code   RO          801    .text               startup_stm32f10x_hd.o
    0x08001454   0x08001454   0x00000002   Code   RO         2431    .text               c_w.l(use_no_semi_2.o)
    0x08001456   0x08001456   0x00000002   PAD
    0x08001458   0x08001458   0x00000018   Code   RO         2437    .text               c_w.l(noretval__2printf.o)
    0x08001470   0x08001470   0x00000028   Code   RO         2439    .text               c_w.l(noretval__2sprintf.o)
    0x08001498   0x08001498   0x00000058   Code   RO         2446    .text               c_w.l(_printf_hex_int.o)
    0x080014f0   0x080014f0   0x0000010e   Code   RO         2472    .text               c_w.l(__printf_wp.o)
    0x080015fe   0x080015fe   0x00000006   Code   RO         2487    .text               c_w.l(heapauxi.o)
    0x08001604   0x08001604   0x00000002   Code   RO         2555    .text               c_w.l(use_no_semi.o)
    0x08001606   0x08001606   0x00000016   Code   RO         2558    .text               c_w.l(_rserrno.o)
    0x0800161c   0x0800161c   0x000000b2   Code   RO         2560    .text               c_w.l(_printf_intcommon.o)
    0x080016ce   0x080016ce   0x0000041e   Code   RO         2562    .text               c_w.l(_printf_fp_dec.o)
    0x08001aec   0x08001aec   0x00000030   Code   RO         2564    .text               c_w.l(_printf_char_common.o)
    0x08001b1c   0x08001b1c   0x0000000a   Code   RO         2566    .text               c_w.l(_sputc.o)
    0x08001b26   0x08001b26   0x00000002   PAD
    0x08001b28   0x08001b28   0x00000024   Code   RO         2568    .text               c_w.l(_printf_char_file.o)
    0x08001b4c   0x08001b4c   0x00000008   Code   RO         2632    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001b54   0x08001b54   0x00000008   Code   RO         2637    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08001b5c   0x08001b5c   0x0000008a   Code   RO         2639    .text               c_w.l(lludiv10.o)
    0x08001be6   0x08001be6   0x00000002   PAD
    0x08001be8   0x08001be8   0x00000080   Code   RO         2641    .text               c_w.l(_printf_fp_infnan.o)
    0x08001c68   0x08001c68   0x000000e4   Code   RO         2645    .text               c_w.l(bigflt0.o)
    0x08001d4c   0x08001d4c   0x00000008   Code   RO         2670    .text               c_w.l(ferror.o)
    0x08001d54   0x08001d54   0x00000008   Code   RO         2693    .text               c_w.l(libspace.o)
    0x08001d5c   0x08001d5c   0x0000004a   Code   RO         2696    .text               c_w.l(sys_stackheap_outer.o)
    0x08001da6   0x08001da6   0x00000012   Code   RO         2698    .text               c_w.l(exit.o)
    0x08001db8   0x08001db8   0x00000080   Code   RO         2700    .text               c_w.l(strcmpv7m.o)
    0x08001e38   0x08001e38   0x0000003e   Code   RO         2648    CL$$btod_d2e        c_w.l(btod.o)
    0x08001e76   0x08001e76   0x00000046   Code   RO         2650    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001ebc   0x08001ebc   0x00000060   Code   RO         2649    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08001f1c   0x08001f1c   0x00000338   Code   RO         2658    CL$$btod_div_common  c_w.l(btod.o)
    0x08002254   0x08002254   0x000000dc   Code   RO         2655    CL$$btod_e2e        c_w.l(btod.o)
    0x08002330   0x08002330   0x0000002a   Code   RO         2652    CL$$btod_ediv       c_w.l(btod.o)
    0x0800235a   0x0800235a   0x0000002a   Code   RO         2651    CL$$btod_emul       c_w.l(btod.o)
    0x08002384   0x08002384   0x00000244   Code   RO         2657    CL$$btod_mult_common  c_w.l(btod.o)
    0x080025c8   0x080025c8   0x00000016   Code   RO          847    i.ADC_Cmd           stm32f10x_adc.o
    0x080025de   0x080025de   0x00000016   Code   RO          848    i.ADC_DMACmd        stm32f10x_adc.o
    0x080025f4   0x080025f4   0x0000005c   Code   RO          849    i.ADC_DeInit        stm32f10x_adc.o
    0x08002650   0x08002650   0x00000014   Code   RO          855    i.ADC_GetCalibrationStatus  stm32f10x_adc.o
    0x08002664   0x08002664   0x00000014   Code   RO          861    i.ADC_GetResetCalibrationStatus  stm32f10x_adc.o
    0x08002678   0x08002678   0x00000050   Code   RO          865    i.ADC_Init          stm32f10x_adc.o
    0x080026c8   0x080026c8   0x000000b8   Code   RO          869    i.ADC_RegularChannelConfig  stm32f10x_adc.o
    0x08002780   0x08002780   0x0000000a   Code   RO          870    i.ADC_ResetCalibration  stm32f10x_adc.o
    0x0800278a   0x0800278a   0x00000016   Code   RO          872    i.ADC_SoftwareStartConvCmd  stm32f10x_adc.o
    0x080027a0   0x080027a0   0x0000000a   Code   RO          874    i.ADC_StartCalibration  stm32f10x_adc.o
    0x080027aa   0x080027aa   0x00000002   PAD
    0x080027ac   0x080027ac   0x000000b4   Code   RO          355    i.Adc_Init          adc.o
    0x08002860   0x08002860   0x00000038   Code   RO          367    i.BEEP_Init         beep.o
    0x08002898   0x08002898   0x00000004   Code   RO          232    i.BusFault_Handler  stm32f10x_it.o
    0x0800289c   0x0800289c   0x00000028   Code   RO         2297    i.DAC_Cmd           stm32f10x_dac.o
    0x080028c4   0x080028c4   0x00000034   Code   RO         2302    i.DAC_Init          stm32f10x_dac.o
    0x080028f8   0x080028f8   0x00000020   Code   RO         2303    i.DAC_SetChannel1Data  stm32f10x_dac.o
    0x08002918   0x08002918   0x0000005c   Code   RO            1    i.DMA1_Channel1_IRQHandler  main.o
    0x08002974   0x08002974   0x0000001c   Code   RO         1064    i.DMA_ClearITPendingBit  stm32f10x_dma.o
    0x08002990   0x08002990   0x00000018   Code   RO         1065    i.DMA_Cmd           stm32f10x_dma.o
    0x080029a8   0x080029a8   0x0000014c   Code   RO         1066    i.DMA_DeInit        stm32f10x_dma.o
    0x08002af4   0x08002af4   0x0000002c   Code   RO         1069    i.DMA_GetITStatus   stm32f10x_dma.o
    0x08002b20   0x08002b20   0x00000012   Code   RO         1070    i.DMA_ITConfig      stm32f10x_dma.o
    0x08002b32   0x08002b32   0x0000003c   Code   RO         1071    i.DMA_Init          stm32f10x_dma.o
    0x08002b6e   0x08002b6e   0x00000002   PAD
    0x08002b70   0x08002b70   0x0000005c   Code   RO          382    i.Dac1_Init         dac.o
    0x08002bcc   0x08002bcc   0x00000060   Code   RO          383    i.Dac2_Init         dac.o
    0x08002c2c   0x08002c2c   0x00000002   Code   RO          233    i.DebugMon_Handler  stm32f10x_it.o
    0x08002c2e   0x08002c2e   0x00000002   PAD
    0x08002c30   0x08002c30   0x000000a0   Code   RO            2    i.EXTI0_IRQHandler  main.o
    0x08002cd0   0x08002cd0   0x00000064   Code   RO            3    i.EXTI3_IRQHandler  main.o
    0x08002d34   0x08002d34   0x00000064   Code   RO            4    i.EXTI4_IRQHandler  main.o
    0x08002d98   0x08002d98   0x000000b0   Code   RO          400    i.EXTIX_Init        exti.o
    0x08002e48   0x08002e48   0x0000000c   Code   RO         2376    i.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x08002e54   0x08002e54   0x00000094   Code   RO         2381    i.EXTI_Init         stm32f10x_exti.o
    0x08002ee8   0x08002ee8   0x00000034   Code   RO         1640    i.FSMC_NORSRAMCmd   stm32f10x_fsmc.o
    0x08002f1c   0x08002f1c   0x000000e6   Code   RO         1642    i.FSMC_NORSRAMInit  stm32f10x_fsmc.o
    0x08003002   0x08003002   0x00000002   PAD
    0x08003004   0x08003004   0x00000040   Code   RO         1138    i.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08003044   0x08003044   0x00000116   Code   RO         1141    i.GPIO_Init         stm32f10x_gpio.o
    0x0800315a   0x0800315a   0x00000012   Code   RO         1145    i.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x0800316c   0x0800316c   0x00000004   Code   RO         1148    i.GPIO_ResetBits    stm32f10x_gpio.o
    0x08003170   0x08003170   0x00000004   Code   RO         1149    i.GPIO_SetBits      stm32f10x_gpio.o
    0x08003174   0x08003174   0x00000120   Code   RO            5    i.GetPowerMag       main.o
    0x08003294   0x08003294   0x00000004   Code   RO          234    i.HardFault_Handler  stm32f10x_it.o
    0x08003298   0x08003298   0x00000078   Code   RO            6    i.InitBufInArray    main.o
    0x08003310   0x08003310   0x0000003c   Code   RO          670    i.KEY_Init          key.o
    0x0800334c   0x0800334c   0x0000006c   Code   RO          425    i.LCD_Clear         lcd.o
    0x080033b8   0x080033b8   0x000001c0   Code   RO          429    i.LCD_Display_Dir   lcd.o
    0x08003578   0x08003578   0x000000b0   Code   RO          430    i.LCD_DrawLine      lcd.o
    0x08003628   0x08003628   0x00000024   Code   RO          431    i.LCD_DrawPoint     lcd.o
    0x0800364c   0x0800364c   0x0000017c   Code   RO          434    i.LCD_Fast_DrawPoint  lcd.o
    0x080037c8   0x080037c8   0x0000376c   Code   RO          436    i.LCD_Init          lcd.o
    0x08006f34   0x08006f34   0x00000016   Code   RO          437    i.LCD_Pow           lcd.o
    0x08006f4a   0x08006f4a   0x00000002   PAD
    0x08006f4c   0x08006f4c   0x00000014   Code   RO          438    i.LCD_RD_DATA       lcd.o
    0x08006f60   0x08006f60   0x00000016   Code   RO          440    i.LCD_ReadReg       lcd.o
    0x08006f76   0x08006f76   0x00000002   PAD
    0x08006f78   0x08006f78   0x00000054   Code   RO          441    i.LCD_SSD_BackLightSet  lcd.o
    0x08006fcc   0x08006fcc   0x000002ec   Code   RO          442    i.LCD_Scan_Dir      lcd.o
    0x080072b8   0x080072b8   0x000001a8   Code   RO          443    i.LCD_SetCursor     lcd.o
    0x08007460   0x08007460   0x00000128   Code   RO          445    i.LCD_ShowChar      lcd.o
    0x08007588   0x08007588   0x00000094   Code   RO          446    i.LCD_ShowNum       lcd.o
    0x0800761c   0x0800761c   0x00000066   Code   RO          447    i.LCD_ShowString    lcd.o
    0x08007682   0x08007682   0x00000002   PAD
    0x08007684   0x08007684   0x0000000c   Code   RO          449    i.LCD_WR_DATA       lcd.o
    0x08007690   0x08007690   0x0000000c   Code   RO          450    i.LCD_WR_REG        lcd.o
    0x0800769c   0x0800769c   0x00000014   Code   RO          452    i.LCD_WriteRAM_Prepare  lcd.o
    0x080076b0   0x080076b0   0x00000010   Code   RO          453    i.LCD_WriteReg      lcd.o
    0x080076c0   0x080076c0   0x0000004c   Code   RO          658    i.LED_Init          led.o
    0x0800770c   0x0800770c   0x0000007c   Code   RO          412    i.MYDMA1_Config     dma.o
    0x08007788   0x08007788   0x00000004   Code   RO          235    i.MemManage_Handler  stm32f10x_it.o
    0x0800778c   0x0800778c   0x00000002   Code   RO          236    i.NMI_Handler       stm32f10x_it.o
    0x0800778e   0x0800778e   0x00000002   PAD
    0x08007790   0x08007790   0x00000070   Code   RO          805    i.NVIC_Init         misc.o
    0x08007800   0x08007800   0x00000014   Code   RO          806    i.NVIC_PriorityGroupConfig  misc.o
    0x08007814   0x08007814   0x00000002   Code   RO          237    i.PendSV_Handler    stm32f10x_it.o
    0x08007816   0x08007816   0x00000002   PAD
    0x08007818   0x08007818   0x00000018   Code   RO         1249    i.RCC_ADCCLKConfig  stm32f10x_rcc.o
    0x08007830   0x08007830   0x00000020   Code   RO         1250    i.RCC_AHBPeriphClockCmd  stm32f10x_rcc.o
    0x08007850   0x08007850   0x00000020   Code   RO         1251    i.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08007870   0x08007870   0x00000020   Code   RO         1253    i.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x08007890   0x08007890   0x00000020   Code   RO         1254    i.RCC_APB2PeriphResetCmd  stm32f10x_rcc.o
    0x080078b0   0x080078b0   0x000000d4   Code   RO         1261    i.RCC_GetClocksFreq  stm32f10x_rcc.o
    0x08007984   0x08007984   0x00000002   Code   RO          238    i.SVC_Handler       stm32f10x_it.o
    0x08007986   0x08007986   0x00000008   Code   RO          321    i.SetSysClock       system_stm32f10x.o
    0x0800798e   0x0800798e   0x00000002   PAD
    0x08007990   0x08007990   0x000000e0   Code   RO          322    i.SetSysClockTo72   system_stm32f10x.o
    0x08007a70   0x08007a70   0x00000028   Code   RO          809    i.SysTick_CLKSourceConfig  misc.o
    0x08007a98   0x08007a98   0x00000002   Code   RO          239    i.SysTick_Handler   stm32f10x_it.o
    0x08007a9a   0x08007a9a   0x00000002   PAD
    0x08007a9c   0x08007a9c   0x00000060   Code   RO          324    i.SystemInit        system_stm32f10x.o
    0x08007afc   0x08007afc   0x00000088   Code   RO          634    i.TIM2_PWM_Init     timer.o
    0x08007b84   0x08007b84   0x00000020   Code   RO            7    i.TIM3_IRQHandler   main.o
    0x08007ba4   0x08007ba4   0x00000068   Code   RO          635    i.TIM3_Int_Init     timer.o
    0x08007c0c   0x08007c0c   0x00000040   Code   RO          636    i.TIM4_Int_Init     timer.o
    0x08007c4c   0x08007c4c   0x00000006   Code   RO         1760    i.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08007c52   0x08007c52   0x00000018   Code   RO         1765    i.TIM_Cmd           stm32f10x_tim.o
    0x08007c6a   0x08007c6a   0x0000001e   Code   RO         1767    i.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08007c88   0x08007c88   0x00000022   Code   RO         1786    i.TIM_GetITStatus   stm32f10x_tim.o
    0x08007caa   0x08007caa   0x00000012   Code   RO         1790    i.TIM_ITConfig      stm32f10x_tim.o
    0x08007cbc   0x08007cbc   0x000000a4   Code   RO         1799    i.TIM_OC2Init       stm32f10x_tim.o
    0x08007d60   0x08007d60   0x00000006   Code   RO         1814    i.TIM_PrescalerConfig  stm32f10x_tim.o
    0x08007d66   0x08007d66   0x00000012   Code   RO         1822    i.TIM_SelectOutputTrigger  stm32f10x_tim.o
    0x08007d78   0x08007d78   0x000000a4   Code   RO         1836    i.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08007e1c   0x08007e1c   0x00000088   Code   RO          719    i.USART1_IRQHandler  usart.o
    0x08007ea4   0x08007ea4   0x00000018   Code   RO         1453    i.USART_Cmd         stm32f10x_usart.o
    0x08007ebc   0x08007ebc   0x00000054   Code   RO         1457    i.USART_GetITStatus  stm32f10x_usart.o
    0x08007f10   0x08007f10   0x0000004a   Code   RO         1459    i.USART_ITConfig    stm32f10x_usart.o
    0x08007f5a   0x08007f5a   0x00000002   PAD
    0x08007f5c   0x08007f5c   0x000000d8   Code   RO         1460    i.USART_Init        stm32f10x_usart.o
    0x08008034   0x08008034   0x0000000a   Code   RO         1467    i.USART_ReceiveData  stm32f10x_usart.o
    0x0800803e   0x0800803e   0x00000004   Code   RO          240    i.UsageFault_Handler  stm32f10x_it.o
    0x08008042   0x08008042   0x00000028   Code   RO         2689    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x0800806a   0x0800806a   0x00000002   PAD
    0x0800806c   0x0800806c   0x00000388   Code   RO         2603    i.__ieee754_rem_pio2  m_ws.l(rred.o)
    0x080083f4   0x080083f4   0x000000fc   Code   RO         2586    i.__kernel_cos      m_ws.l(cos_i.o)
    0x080084f0   0x080084f0   0x000000aa   Code   RO         2691    i.__kernel_poly     m_ws.l(poly.o)
    0x0800859a   0x0800859a   0x00000002   PAD
    0x0800859c   0x0800859c   0x000000ec   Code   RO         2608    i.__kernel_sin      m_ws.l(sin_i.o)
    0x08008688   0x08008688   0x00000006   Code   RO         2590    i.__mathlib_dbl_infnan  m_ws.l(dunder.o)
    0x0800868e   0x0800868e   0x0000000c   Code   RO         2592    i.__mathlib_dbl_invalid  m_ws.l(dunder.o)
    0x0800869a   0x0800869a   0x00000002   PAD
    0x0800869c   0x0800869c   0x00000010   Code   RO         2595    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x080086ac   0x080086ac   0x0000000e   Code   RO         2474    i._is_digit         c_w.l(__printf_wp.o)
    0x080086ba   0x080086ba   0x00000006   Code   RO          720    i._sys_exit         usart.o
    0x080086c0   0x080086c0   0x00000178   Code   RO            8    i.clear_point       main.o
    0x08008838   0x08008838   0x00000044   Code   RO          693    i.delay_init        delay.o
    0x0800887c   0x0800887c   0x0000004c   Code   RO          694    i.delay_ms          delay.o
    0x080088c8   0x080088c8   0x0000004c   Code   RO          695    i.delay_us          delay.o
    0x08008914   0x08008914   0x0000001c   Code   RO          721    i.fputc             usart.o
    0x08008930   0x08008930   0x00000018   Code   RO            9    i.lcd_huadian       main.o
    0x08008948   0x08008948   0x00000024   Code   RO           10    i.lcd_huaxian       main.o
    0x0800896c   0x0800896c   0x0000024c   Code   RO           11    i.main              main.o
    0x08008bb8   0x08008bb8   0x000000a0   Code   RO         2540    i.sin               m_ws.l(sin.o)
    0x08008c58   0x08008c58   0x00000034   Code   RO           12    i.sinout            main.o
    0x08008c8c   0x08008c8c   0x0000004c   Code   RO         2548    i.sqrt              m_ws.l(sqrt.o)
    0x08008cd8   0x08008cd8   0x000000a0   Code   RO          722    i.uart_init         usart.o
    0x08008d78   0x08008d78   0x00000244   Code   RO           13    i.window            main.o
    0x08008fbc   0x08008fbc   0x0000002c   Code   RO         2675    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008fe8   0x08008fe8   0x00000062   Code   RO         2491    x$fpl$d2f           fz_ws.l(d2f.o)
    0x0800904a   0x0800904a   0x00000002   PAD
    0x0800904c   0x0800904c   0x00000150   Code   RO         2493    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x0800919c   0x0800919c   0x00000010   Code   RO         2753    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x080091ac   0x080091ac   0x000002b0   Code   RO         2500    x$fpl$ddiv          fz_ws.l(ddiv.o)
    0x0800945c   0x0800945c   0x0000005e   Code   RO         2679    x$fpl$dfix          fz_ws.l(dfix.o)
    0x080094ba   0x080094ba   0x00000002   PAD
    0x080094bc   0x080094bc   0x0000005a   Code   RO         2503    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x08009516   0x08009516   0x0000002e   Code   RO         2508    x$fpl$dflt          fz_ws.l(dflt_clz.o)
    0x08009544   0x08009544   0x00000026   Code   RO         2507    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x0800956a   0x0800956a   0x00000002   PAD
    0x0800956c   0x0800956c   0x00000154   Code   RO         2513    x$fpl$dmul          fz_ws.l(dmul.o)
    0x080096c0   0x080096c0   0x0000009c   Code   RO         2573    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x0800975c   0x0800975c   0x0000000c   Code   RO         2575    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08009768   0x08009768   0x00000016   Code   RO         2494    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x0800977e   0x0800977e   0x00000002   PAD
    0x08009780   0x08009780   0x000001cc   Code   RO         2577    x$fpl$dsqrt         fz_ws.l(dsqrt_noumaal.o)
    0x0800994c   0x0800994c   0x000001d4   Code   RO         2495    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x08009b20   0x08009b20   0x00000056   Code   RO         2515    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08009b76   0x08009b76   0x00000002   PAD
    0x08009b78   0x08009b78   0x000000c4   Code   RO         2517    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x08009c3c   0x08009c3c   0x00000018   Code   RO         2683    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x08009c54   0x08009c54   0x0000003e   Code   RO         2523    x$fpl$ffixu         fz_ws.l(ffixu.o)
    0x08009c92   0x08009c92   0x00000002   PAD
    0x08009c94   0x08009c94   0x00000030   Code   RO         2528    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08009cc4   0x08009cc4   0x00000068   Code   RO         2579    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08009d2c   0x08009d2c   0x00000102   Code   RO         2533    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08009e2e   0x08009e2e   0x0000008c   Code   RO         2581    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08009eba   0x08009eba   0x0000000a   Code   RO         2583    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08009ec4   0x08009ec4   0x00000062   Code   RO         2535    x$fpl$frleqf        fz_ws.l(frleqf.o)
    0x08009f26   0x08009f26   0x00000002   PAD
    0x08009f28   0x08009f28   0x000000ea   Code   RO         2519    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x0800a012   0x0800a012   0x00000004   Code   RO         2537    x$fpl$printf1       fz_ws.l(printf1.o)
    0x0800a016   0x0800a016   0x00000064   Code   RO         2761    x$fpl$retnan        fz_ws.l(retnan.o)
    0x0800a07a   0x0800a07a   0x0000005c   Code   RO         2687    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x0800a0d6   0x0800a0d6   0x00000030   Code   RO         2774    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x0800a106   0x0800a106   0x00000000   Code   RO         2585    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x0800a106   0x0800a106   0x000017c0   Data   RO          456    .constdata          lcd.o
    0x0800b8c6   0x0800b8c6   0x00000028   Data   RO         2447    .constdata          c_w.l(_printf_hex_int.o)
    0x0800b8ee   0x0800b8ee   0x00000002   PAD
    0x0800b8f0   0x0800b8f0   0x00000030   Data   RO         2587    .constdata          m_ws.l(cos_i.o)
    0x0800b920   0x0800b920   0x000000c8   Data   RO         2605    .constdata          m_ws.l(rred.o)
    0x0800b9e8   0x0800b9e8   0x00000028   Data   RO         2609    .constdata          m_ws.l(sin_i.o)
    0x0800ba10   0x0800ba10   0x00000094   Data   RO         2646    .constdata          c_w.l(bigflt0.o)
    0x0800baa4   0x0800baa4   0x00000020   Data   RO         2834    Region$$Table       anon$$obj.o
    0x0800bac4   0x0800bac4   0x0000001c   Data   RO         2674    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800bae0, Size: 0x00004fa8, Max: 0x00010000, ABSOLUTE, COMPRESSED[0x00000c50])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00001036   Data   RW           15    .data               main.o
    0x20001036   COMPRESSED   0x00000002   PAD
    0x20001038   COMPRESSED   0x00000014   Data   RW          325    .data               system_stm32f10x.o
    0x2000104c   COMPRESSED   0x00000004   Data   RW          457    .data               lcd.o
    0x20001050   COMPRESSED   0x00000004   Data   RW          696    .data               delay.o
    0x20001054   COMPRESSED   0x00000006   Data   RW          724    .data               usart.o
    0x2000105a   COMPRESSED   0x00000014   Data   RW         1281    .data               stm32f10x_rcc.o
    0x2000106e   COMPRESSED   0x00000002   PAD
    0x20001070        -       0x00003800   Zero   RW           14    .bss                main.o
    0x20004870        -       0x0000000e   Zero   RW          455    .bss                lcd.o
    0x2000487e        -       0x000000c8   Zero   RW          723    .bss                usart.o
    0x20004946   COMPRESSED   0x00000002   PAD
    0x20004948        -       0x00000060   Zero   RW         2694    .bss                c_w.l(libspace.o)
    0x200049a8        -       0x00000200   Zero   RW          799    HEAP                startup_stm32f10x_hd.o
    0x20004ba8        -       0x00000400   Zero   RW          798    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       180          8          0          0          0        675   adc.o
        56          6          0          0          0        544   beep.o
      4576       4080          0          0          0        436   cr4_fft_1024_stm32.o
       188         10          0          0          0       1264   dac.o
       220         26          0          4          0       1927   delay.o
       124          4          0          0          0       1224   dma.o
       176          0          0          0          0        673   exti.o
        60          8          0          0          0        547   key.o
     17262        192       6080          4         14      24458   lcd.o
        76          8          0          0          0        551   led.o
      2548        386          0       4150      14336     297072   main.o
       172         22          0          0          0       2257   misc.o
        64         26        304          0       1536        844   startup_stm32f10x_hd.o
       482         24          0          0          0       8522   stm32f10x_adc.o
       124         18          0          0          0       2603   stm32f10x_dac.o
       506         26          0          0          0       5491   stm32f10x_dma.o
       160         12          0          0          0       1653   stm32f10x_exti.o
       282          6          0          0          0       1965   stm32f10x_fsmc.o
       368          4          0          0          0       4574   stm32f10x_gpio.o
        26          0          0          0          0       4154   stm32f10x_it.o
       364         50          0         20          0       6522   stm32f10x_rcc.o
       464         52          0          0          0       6469   stm32f10x_tim.o
       408          6          0          0          0       5294   stm32f10x_usart.o
       328         28          0         20          0       2337   system_stm32f10x.o
       304         16          0          0          0       2176   timer.o
       330         26          0          6        200       4444   usart.o

    ----------------------------------------------------------------------
     29872       <USER>       <GROUP>       4208      16088     388676   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          0          4          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       100          0          0          0          0          0   __dclz77c.o
         8          0          0          0          0         68   __main.o
       284          0          0          0          0        156   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
        88          4         40          0          0         88   _printf_hex_int.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        10          0          0          0          0         68   _sputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        18          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        24          4          0          0          0         84   noretval__2printf.o
        40          6          0          0          0         84   noretval__2sprintf.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        98          4          0          0          0         92   d2f.o
       826         16          0          0          0        348   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
       688        140          0          0          0        208   ddiv.o
        94          4          0          0          0         92   dfix.o
        90          4          0          0          0         92   dfixu.o
        84          0          0          0          0        136   dflt_clz.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       460         56          0          0          0        120   dsqrt_noumaal.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        24          0          0          0          0         68   fcmpi.o
        62          4          0          0          0         84   ffixu.o
        48          0          0          0          0         68   fflt_clz.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
        98          0          0          0          0         68   frleqf.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
       252         22         48          0          0        124   cos_i.o
        34          6          0          0          0        204   dunder.o
        40          0          0          0          0         68   fpclassify.o
       170          0          0          0          0         96   poly.o
       904         76        200          0          0        140   rred.o
       160         10          0          0          0        108   sin.o
       236         12         40          0          0        128   sin_i.o
        76          0          0          0          0         84   sqrt.o

    ----------------------------------------------------------------------
     11046        <USER>        <GROUP>          0         96       6428   Library Totals
        28          0          2          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      4778        202        216          0         96       2924   c_w.l
      4368        272          0          0          0       2552   fz_ws.l
      1872        126        288          0          0        952   m_ws.l

    ----------------------------------------------------------------------
     11046        <USER>        <GROUP>          0         96       6428   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     40918       5644       6922       4208      16184     385844   Grand Totals
     40918       5644       6922       3152      16184     385844   ELF Image Totals (compressed)
     40918       5644       6922       3152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                47840 (  46.72kB)
    Total RW  Size (RW Data + ZI Data)             20392 (  19.91kB)
    Total ROM Size (Code + RO Data + RW Data)      50992 (  49.80kB)

==============================================================================

